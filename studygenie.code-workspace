{"folders": [{"name": "StudyGenie Root", "path": "."}, {"name": "Frontend (React)", "path": "./frontend"}, {"name": "Backend (FastAPI)", "path": "./backend"}], "settings": {"python.defaultInterpreterPath": "/usr/bin/python3", "python.terminal.activateEnvironment": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "files.exclude": {"**/node_modules": true, "**/__pycache__": true, "**/venv": true, "**/env": true, "**/.git": true}}, "launch": {"version": "0.2.0", "configurations": [{"name": "Python: FastAPI", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/server.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "envFile": "${workspaceFolder}/backend/.env"}]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "Start Backend", "type": "shell", "command": "python", "args": ["server.py"], "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Frontend", "type": "shell", "command": "yarn", "args": ["start"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Both Services", "dependsOrder": "parallel", "dependsOn": ["Start Backend", "Start Frontend"]}]}, "extensions": {"recommendations": ["ms-python.python", "ms-python.pylance", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "rangav.vscode-thunder-client", "dsznajder.es7-react-js-snippets"]}}